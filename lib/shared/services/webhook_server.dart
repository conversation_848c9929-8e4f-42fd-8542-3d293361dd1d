import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';

/// Simple HTTP server to receive webhook calls from ElevenLabs
class WebhookServer {
  HttpServer? _server;
  static const int port = 8080;
  
  // Callback for counter actions
  Function(String action, {int? value, int? amount})? onCounterAction;
  
  Future<void> start() async {
    try {
      _server = await HttpServer.bind(InternetAddress.anyIPv4, port);
      debugPrint('🚀 Webhook server started on port $port');
      
      await for (HttpRequest request in _server!) {
        _handleRequest(request);
      }
    } catch (e) {
      debugPrint('❌ Error starting webhook server: $e');
    }
  }
  
  void _handleRequest(HttpRequest request) async {
    // Enable CORS for web requests
    request.response.headers.add('Access-Control-Allow-Origin', '*');
    request.response.headers.add('Access-Control-Allow-Methods', 'POST, GET, OPTIONS');
    request.response.headers.add('Access-Control-Allow-Headers', 'Content-Type');
    
    if (request.method == 'OPTIONS') {
      request.response.statusCode = 200;
      await request.response.close();
      return;
    }
    
    if (request.method == 'POST' && request.uri.path == '/api/counter') {
      await _handleCounterRequest(request);
    } else {
      request.response.statusCode = 404;
      request.response.write('Not Found');
      await request.response.close();
    }
  }
  
  Future<void> _handleCounterRequest(HttpRequest request) async {
    try {
      final body = await utf8.decoder.bind(request).join();
      final data = jsonDecode(body) as Map<String, dynamic>;
      
      debugPrint('📨 Received counter webhook: $data');
      
      final action = data['action'] as String?;
      final value = data['value'] as int?;
      final amount = data['amount'] as int? ?? 1;
      
      if (action != null && onCounterAction != null) {
        onCounterAction!(action, value: value, amount: amount);
        
        // Send success response
        request.response.statusCode = 200;
        request.response.headers.contentType = ContentType.json;
        request.response.write(jsonEncode({
          'success': true,
          'message': 'Counter action executed: $action',
          'timestamp': DateTime.now().toIso8601String(),
        }));
      } else {
        request.response.statusCode = 400;
        request.response.write('Invalid request');
      }
    } catch (e) {
      debugPrint('❌ Error handling counter request: $e');
      request.response.statusCode = 500;
      request.response.write('Internal Server Error');
    }
    
    await request.response.close();
  }
  
  Future<void> stop() async {
    await _server?.close();
    _server = null;
    debugPrint('🛑 Webhook server stopped');
  }
  
  String get webhookUrl => 'http://localhost:$port/api/counter';
}
