import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../shared/services/simple_elevenlabs_service.dart';
import '../../../../shared/models/simple_conversation_models.dart';
import '../../../../shared/widgets/glass_morphism_card.dart';
import '../../../../core/theme/color_palette.dart';

class SimpleVoiceChatScreen extends StatefulWidget {
  const SimpleVoiceChatScreen({super.key});

  @override
  State<SimpleVoiceChatScreen> createState() => _SimpleVoiceChatScreenState();
}

class _SimpleVoiceChatScreenState extends State<SimpleVoiceChatScreen> {
  final SimpleElevenLabsService _service = SimpleElevenLabsService();
  int _counter = 0;

  @override
  void initState() {
    super.initState();
    _service.addListener(_updateUI);
    _setupClientTools();
  }

  void _setupClientTools() {
    // Set up the client tool callback
    _service.onClientToolCall = (String toolName, Map<String, dynamic> parameters) {
      debugPrint('🎯 Received client tool call: $toolName with parameters: $parameters');

      if (toolName == 'counter_control') {
        final action = parameters['action'] as String?;
        final value = parameters['value'] as int?;
        final amount = parameters['amount'] as int? ?? 1;

        switch (action) {
          case 'increment':
            for (int i = 0; i < amount; i++) {
              _incrementCounter();
            }
            break;
          case 'decrement':
            for (int i = 0; i < amount; i++) {
              _decrementCounter();
            }
            break;
          case 'set':
            if (value != null) {
              _setCounter(value);
            }
            break;
          case 'reset':
            _resetCounter();
            break;
        }
      }
    };

    debugPrint('✅ Client tools configured for counter control');
  }

  @override
  void dispose() {
    _service.removeListener(_updateUI);
    _service.dispose();
    super.dispose();
  }

  void _updateUI() {
    if (mounted) {
      setState(() {});
    }
  }

  void _incrementCounter() {
    HapticFeedback.lightImpact();
    setState(() {
      _counter++;
    });
  }

  void _decrementCounter() {
    HapticFeedback.lightImpact();
    setState(() {
      if (_counter > 0) {
        _counter--;
      }
    });
  }

  void _setCounter(int value) {
    HapticFeedback.lightImpact();
    setState(() {
      _counter = value.clamp(0, 9999); // Reasonable upper limit
    });
  }

  void _resetCounter() {
    HapticFeedback.lightImpact();
    setState(() {
      _counter = 0;
    });
  }

  Widget _buildCounterSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32),
      child: GlassMorphismCard(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            const Text(
              'Counter',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // Decrement button
                GestureDetector(
                  onTap: _decrementCounter,
                  child: Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        colors: [
                          AppColorPalette.primaryOrange,
                          AppColorPalette.primaryOrangeLight,
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: AppColorPalette.primaryOrange.withValues(alpha: 0.3),
                          blurRadius: 8,
                          spreadRadius: 2,
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.remove,
                      color: Colors.white,
                      size: 30,
                    ),
                  ),
                ),

                // Counter display
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.white.withValues(alpha: 0.1),
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.2),
                      width: 2,
                    ),
                  ),
                  child: Center(
                    child: Text(
                      '$_counter',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),

                // Increment button
                GestureDetector(
                  onTap: _incrementCounter,
                  child: Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        colors: [
                          AppColorPalette.primaryOrange,
                          AppColorPalette.primaryOrangeLight,
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: AppColorPalette.primaryOrange.withValues(alpha: 0.3),
                          blurRadius: 8,
                          spreadRadius: 2,
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.add,
                      color: Colors.white,
                      size: 30,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final service = _service;
    
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        title: const Text('Voice Chat'),
        centerTitle: true,
      ),
      body: SafeArea(
        child: Column(
          children: [
            // Status indicator
            Expanded(
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Animated avatar
                    Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: LinearGradient(
                          colors: _getStatusColors(service.status),
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: _getStatusColors(service.status).first.withValues(alpha: 0.3),
                            blurRadius: 20,
                            spreadRadius: 5,
                          ),
                        ],
                      ),
                      child: Icon(
                        _getStatusIcon(service.status),
                        size: 60,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 32),
                    
                    // Status text
                    Text(
                      _getStatusText(service.status),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    // Last transcript
                    if (service.lastTranscript.isNotEmpty)
                      Container(
                        margin: const EdgeInsets.symmetric(horizontal: 32),
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          children: [
                            const Text(
                              'You said:',
                              style: TextStyle(
                                color: Colors.grey,
                                fontSize: 14,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              service.lastTranscript,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    
                    // Last response
                    if (service.lastResponse.isNotEmpty) ...[
                      const SizedBox(height: 16),
                      Container(
                        margin: const EdgeInsets.symmetric(horizontal: 32),
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.orange.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          children: [
                            const Text(
                              'Assistant:',
                              style: TextStyle(
                                color: Colors.orange,
                                fontSize: 14,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              service.lastResponse,
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),

            // Counter section
            const SizedBox(height: 20),
            _buildCounterSection(),

            // Control button
            Padding(
              padding: const EdgeInsets.all(32),
              child: SizedBox(
                width: 200,
                height: 60,
                child: ElevatedButton(
                  onPressed: () async {
                    if (service.status == SimpleConversationStatus.disconnected) {
                      await service.startConversation();
                    } else {
                      await service.stopConversation();
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: service.status == SimpleConversationStatus.disconnected
                        ? Colors.orange
                        : Colors.red,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30),
                    ),
                  ),
                  child: Text(
                    service.status == SimpleConversationStatus.disconnected
                        ? 'Start Conversation'
                        : 'Stop Conversation',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  List<Color> _getStatusColors(SimpleConversationStatus status) {
    switch (status) {
      case SimpleConversationStatus.disconnected:
        return [Colors.grey, Colors.grey.shade700];
      case SimpleConversationStatus.connecting:
        return [Colors.yellow, Colors.orange];
      case SimpleConversationStatus.connected:
        return [Colors.green, Colors.green.shade700];
      case SimpleConversationStatus.speaking:
        return [Colors.blue, Colors.blue.shade700];
      case SimpleConversationStatus.listening:
        return [Colors.purple, Colors.purple.shade700];
      case SimpleConversationStatus.error:
        return [Colors.red, Colors.red.shade700];
    }
  }
  
  IconData _getStatusIcon(SimpleConversationStatus status) {
    switch (status) {
      case SimpleConversationStatus.disconnected:
        return Icons.power_settings_new;
      case SimpleConversationStatus.connecting:
        return Icons.sync;
      case SimpleConversationStatus.connected:
      case SimpleConversationStatus.listening:
        return Icons.hearing;
      case SimpleConversationStatus.speaking:
        return Icons.mic;
      case SimpleConversationStatus.error:
        return Icons.error_outline;
    }
  }
  
  String _getStatusText(SimpleConversationStatus status) {
    switch (status) {
      case SimpleConversationStatus.disconnected:
        return 'Tap to start';
      case SimpleConversationStatus.connecting:
        return 'Connecting...';
      case SimpleConversationStatus.connected:
        return 'Listening...';
      case SimpleConversationStatus.speaking:
        return 'Speaking...';
      case SimpleConversationStatus.listening:
        return 'Assistant is speaking...';
      case SimpleConversationStatus.error:
        return 'Error occurred';
    }
  }
}