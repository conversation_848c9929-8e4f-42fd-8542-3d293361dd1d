# ElevenLabs Counter Control Setup

This guide shows how to set up your ElevenLabs agent to control the counter in your Flutter app.

## 🚀 Quick Setup

### 1. Install ngrok (for local testing)
```bash
# Install ngrok
brew install ngrok

# Sign up at https://ngrok.com and get your auth token
ngrok config add-authtoken YOUR_AUTH_TOKEN
```

### 2. Start your Flutter app
Your app now includes a webhook server that starts automatically when you open the voice chat screen.

### 3. Expose the webhook with ngrok
```bash
# In a new terminal, expose port 8080
ngrok http 8080
```

You'll see output like:
```
Forwarding    https://abc123.ngrok.io -> http://localhost:8080
```

### 4. Configure ElevenLabs Agent Tool

In your ElevenLabs agent dashboard, add a new **Webhook** tool:

#### Tool Configuration:
- **Name**: `counter_control`
- **Description**: `Control the workout counter by incrementing, decrementing, or setting a specific value. Use this when the user asks to count reps, sets, or any numerical tracking during their workout.`
- **Method**: `POST`
- **URL**: `https://YOUR_NGROK_URL.ngrok.io/api/counter` (replace with your ngrok URL)

#### Tool Parameters (JSON Schema):
```json
{
  "type": "object",
  "properties": {
    "action": {
      "type": "string",
      "enum": ["increment", "decrement", "set", "reset"],
      "description": "The action to perform on the counter"
    },
    "value": {
      "type": "integer",
      "description": "The value to set (only used with 'set' action)",
      "minimum": 0
    },
    "amount": {
      "type": "integer",
      "description": "Amount to increment/decrement (default: 1)",
      "minimum": 1,
      "default": 1
    }
  },
  "required": ["action"]
}
```

## 🎯 Usage Examples

Once configured, your agent can control the counter with voice commands:

### User Says → Agent Action
- **"Count one rep"** → `{"action": "increment"}`
- **"Add 5 reps"** → `{"action": "increment", "amount": 5}`
- **"Subtract one"** → `{"action": "decrement"}`
- **"Set counter to 10"** → `{"action": "set", "value": 10}`
- **"Reset the counter"** → `{"action": "reset"}`

## 🔧 How It Works

1. **Webhook Server**: Your Flutter app runs a local HTTP server on port 8080
2. **ngrok Tunnel**: Exposes your local server to the internet with HTTPS
3. **ElevenLabs Tool**: Sends POST requests to your webhook when triggered
4. **Counter Updates**: Your app receives the webhook and updates the counter with haptic feedback

## 📱 App Integration

The counter control is integrated into your voice chat screen:

- **Manual Control**: Users can still tap + and - buttons
- **Voice Control**: Agent can control it via webhooks
- **Haptic Feedback**: All actions provide tactile feedback
- **Visual Updates**: Counter updates in real-time

## 🛠 Troubleshooting

### Webhook not working?
1. Check that ngrok is running and the URL is correct
2. Verify the Flutter app is on the voice chat screen (webhook server auto-starts)
3. Check the debug console for webhook messages

### Agent not calling the tool?
1. Make sure the tool description clearly explains when to use it
2. Test with explicit commands like "increment the counter"
3. Check the ElevenLabs agent logs

### Counter not updating?
1. Verify the webhook payload format matches the expected schema
2. Check Flutter debug console for error messages
3. Ensure the app is in the foreground

## 🚀 Production Deployment

For production, replace ngrok with:
- **Heroku/Railway**: Deploy a simple webhook server
- **Supabase Edge Functions**: Serverless webhook handler
- **Firebase Functions**: Cloud-based webhook processing

The webhook server code is in `lib/shared/services/webhook_server.dart` and can be adapted for any backend platform.
