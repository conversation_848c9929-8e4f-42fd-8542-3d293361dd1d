# ElevenLabs Counter Control Setup (No Backend Required!)

This guide shows how to set up your ElevenLabs agent to control the counter in your Flutter app using **Client Tools** - no backend or ngrok needed!

## 🎯 **Client Tools Approach (Recommended)**

Client Tools execute JavaScript directly in the browser/app instead of making HTTP requests. This is much simpler and doesn't require any backend infrastructure.

## 🚀 Quick Setup

### 1. Configure ElevenLabs Agent Tool

In your ElevenLabs agent dashboard, add a new **Client** tool (not Webhook):

#### Tool Configuration:
- **Tool Type**: `Client` (not Webhook!)
- **Name**: `counter_control`
- **Description**: `Control the workout counter by incrementing, decrementing, or setting a specific value. Use this when the user asks to count reps, sets, or any numerical tracking during their workout.`

#### Tool Parameters (JSON Schema):
```json
{
  "type": "object",
  "properties": {
    "action": {
      "type": "string",
      "enum": ["increment", "decrement", "set", "reset"],
      "description": "The action to perform on the counter"
    },
    "value": {
      "type": "integer",
      "description": "The value to set (only used with 'set' action)",
      "minimum": 0
    },
    "amount": {
      "type": "integer",
      "description": "Amount to increment/decrement (default: 1)",
      "minimum": 1,
      "default": 1
    }
  },
  "required": ["action"]
}
```

#### Client Tool JavaScript (leave empty):
The JavaScript field can be left empty since your Flutter app handles the tool execution directly through the WebSocket connection.

### 2. That's it!
No backend, no ngrok, no additional setup required. Your Flutter app is already configured to handle client tool calls.

## 🎯 Usage Examples

Once configured, your agent can control the counter with voice commands:

### User Says → Agent Action
- **"Count one rep"** → `{"action": "increment"}`
- **"Add 5 reps"** → `{"action": "increment", "amount": 5}`
- **"Subtract one"** → `{"action": "decrement"}`
- **"Set counter to 10"** → `{"action": "set", "value": 10}`
- **"Reset the counter"** → `{"action": "reset"}`

## 🔧 How It Works

1. **Client Tools**: ElevenLabs sends tool calls directly through the WebSocket connection
2. **Flutter Handler**: Your app receives and processes tool calls in real-time
3. **Counter Updates**: Counter updates immediately with haptic feedback
4. **Response**: App sends success confirmation back to ElevenLabs

## 📱 App Integration

The counter control is integrated into your voice chat screen:

- **Manual Control**: Users can still tap + and - buttons
- **Voice Control**: Agent controls it via client tools (no backend needed!)
- **Haptic Feedback**: All actions provide tactile feedback
- **Visual Updates**: Counter updates in real-time
- **Seamless**: Works entirely within the existing WebSocket connection

## 🛠 Troubleshooting

### Agent not calling the tool?
1. Make sure you selected **Client** tool type (not Webhook)
2. Ensure the tool description clearly explains when to use it
3. Test with explicit commands like "increment the counter"
4. Check the ElevenLabs agent logs

### Counter not updating?
1. Check Flutter debug console for client tool messages
2. Verify the app is on the voice chat screen (where client tools are handled)
3. Ensure the tool parameters match the expected schema
4. Make sure the app is in the foreground

### Tool calls not being received?
1. Verify your ElevenLabs agent has the client tool properly configured
2. Check that the tool name is exactly `counter_control`
3. Look for debug messages in Flutter console starting with "🎯 Received client tool call"

## ✅ Advantages of Client Tools

- **No Backend Required**: Everything runs in your Flutter app
- **Real-time**: Direct WebSocket communication
- **Secure**: No external endpoints to expose
- **Simple**: No ngrok, servers, or additional infrastructure
- **Reliable**: Works offline and doesn't depend on external services
- **Cost-effective**: No hosting costs for webhook servers

## 🎯 Alternative: Webhook Approach

If you prefer webhooks (requires backend), see the webhook server implementation in `lib/shared/services/webhook_server.dart`. However, client tools are recommended for simplicity.
