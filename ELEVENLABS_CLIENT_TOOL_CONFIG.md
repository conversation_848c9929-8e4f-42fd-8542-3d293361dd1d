# ElevenLabs Client Tool Configuration Guide

## 📋 Step-by-Step Configuration

### 1. In your ElevenLabs Agent Dashboard:

1. **Navigate to Tools section**
2. **Click "Add tool"**
3. **Select "Client" tool type** (NOT Webhook!)

### 2. Fill in the Tool Configuration:

```
Tool Type: Client
Name: counter_control
Description: Control the workout counter by incrementing, decrementing, or setting a specific value. Use this when the user asks to count reps, sets, or any numerical tracking during their workout.
```

### 3. Add the Parameters Schema:

Copy and paste this exact JSON schema into the Parameters field:

```json
{
  "type": "object",
  "properties": {
    "action": {
      "type": "string",
      "enum": ["increment", "decrement", "set", "reset"],
      "description": "The action to perform on the counter"
    },
    "value": {
      "type": "integer",
      "description": "The value to set (only used with 'set' action)",
      "minimum": 0
    },
    "amount": {
      "type": "integer",
      "description": "Amount to increment/decrement (default: 1)",
      "minimum": 1,
      "default": 1
    }
  },
  "required": ["action"]
}
```

### 4. JavaScript Field:

**Leave the JavaScript field EMPTY** - your Flutter app handles the execution.

### 5. Save the Tool

Click "Add tool" to save your configuration.

## 🎯 Testing Your Setup

### Voice Commands to Try:

- **"Count one rep"** → Should increment counter by 1
- **"Add 5 reps"** → Should increment counter by 5
- **"Subtract one"** → Should decrement counter by 1
- **"Set counter to 10"** → Should set counter to 10
- **"Reset the counter"** → Should reset counter to 0

### Expected Behavior:

1. **User speaks** a counter command
2. **Agent recognizes** the intent and calls the `counter_control` tool
3. **Flutter app receives** the client tool call via WebSocket
4. **Counter updates** immediately with haptic feedback
5. **Agent confirms** the action was completed

## 🔍 Debug Information

### In Flutter Debug Console:
Look for these messages to verify everything is working:

```
✅ Client tools configured for counter control
🎯 Received client tool call: counter_control with parameters: {action: increment}
🔧 Client tool call received: {...}
✅ Tool result sent back to agent
```

### In ElevenLabs Agent Logs:
- Tool calls should appear in the conversation logs
- Check for any error messages related to tool execution

## ⚠️ Common Issues

### Tool Not Being Called:
- Verify tool type is "Client" not "Webhook"
- Check tool name is exactly `counter_control`
- Ensure description clearly indicates when to use the tool

### Counter Not Updating:
- Make sure you're on the voice chat screen in your Flutter app
- Check Flutter debug console for error messages
- Verify the app is in the foreground

### Parameters Not Working:
- Double-check the JSON schema matches exactly
- Ensure required fields are marked correctly
- Test with simple commands first (like "increment counter")

## 🎉 Success!

Once configured correctly, your fitness coach agent can seamlessly control the workout counter through voice commands, making your app truly hands-free during workouts! 💪
